{% macro render_axis_join(axis_key, axis_data) %}
{% if axis_data["type"] == "axsm" %}
    INNER JOIN(
    {# Add CTE if available #}
    {% if axis_data["ddl"]["cte"] %}
        WITH {{ axis_data["ddl"]["cte"] }}
    {% endif %}

    {# Add queries with UNION ALL #}
    {% if axis_data["ddl"]["queries"]|length > 0 %}
        {{ axis_data["ddl"]["queries"][0] }} AND movedate BETWEEN '{{ period_start }}' AND '{{ period_end }}'
        {% for query in axis_data["ddl"]["queries"][1:] %}
            UNION ALL
            {{ query }} AND movedate BETWEEN '{{ period_start }}' AND '{{ period_end }}'
        {% endfor %}
    {% endif %}
    ) {{ axis_key }} USING (id_rec)
    {% elif axis_data["type"] == "axss" %}
    INNER JOIN (
    {# Add CTE if available #}
    {% if axis_data["ddl"]["cte"] %}
        WITH {{ axis_data["ddl"]["cte"] }}
    {% endif %}

    {# Add queries with UNION ALL #}
    {% if axis_data["ddl"]["queries"]|length > 0 %}
        {{ axis_data["ddl"]["queries"][0] }}
        {% for query in axis_data["ddl"]["queries"][1:] %}
            UNION ALL
            {{ query }}
        {% endfor %}
    {% endif %}
    ) {{ axis_key }} USING (id_shop)
    {% elif axis_data["type"] == "axsa" %}
    INNER JOIN(
    {# Add CTE if available #}
    {% if axis_data["ddl"]["cte"] %}
        WITH {{ axis_data["ddl"]["cte"] }}
    {% endif %}

    {# Add queries with UNION ALL #}
    {% if axis_data["ddl"]["queries"]|length > 0 %}
        {{ axis_data["ddl"]["queries"][0] }}
        {% for query in axis_data["ddl"]["queries"][1:] %}
            UNION ALL
            {{ query }}
        {% endfor %}
    {% endif %}
    ) {{ axis_key }} USING (article_key, reporting_from)
{% endif %}
{% endmacro %}

SELECT
    hhkey,
    concat(toString(hhkey), toString(purchases.movedate), toString(id_shop)) AS id_trip,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {% if catman %}
                    {{ axis_key }}.position_number AS {{ axis_key }}_position_number,
                {% else %}
                    concat(toString({{ axis_key }}.position_number), '|', toString({{ axis_key }}.autolabel)) AS {{ axis_key }}_position_number,
                {% endif %}
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for fact in required_facts %}
        {% if fact not in ["volume_loyalty_base", "population", "value_loyalty_base", "weight_wave", "BUF", ""] %}
            {% if su_fact["measure"] == fact %}
                {{ fact }} * {{ su_fact["formula"] }} {% if su_fact["measure"] == "volume_rp" %} * 1000 {% endif %} AS {{ fact }},
            {% else %}
                {{ fact }} AS {{ fact }},
            {% endif %}
        {% endif %}
    {% endfor %}
    projectc
FROM pet.purchases_{{ id_panel }} purchases
{% if axes|length == 0 %}
    {{ raise("No axis provided") }}
    {% else %}
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ render_axis_join(axis_key, axis_data) }}
        {% endif %}
    {% endfor %}
{% endif %}
{% for filter_key, filter_data in filters.items() %}
    {% if filter_data["type"] is not none and filter_data["type"] == "flta" %}
        INNER JOIN (
        {% if filter_data["ddl"]["cte"] %}
            {{ filter_data["ddl"]["cte"] }}
        {% endif %}
        {% if filter_data["ddl"]["queries"]|length > 0 %}
            {{ filter_data["ddl"]["queries"][0] }}
            {% for query in filter_data["ddl"]["queries"][1:] %}
                UNION ALL
                {{ query }}
            {% endfor %}
        {% endif %}
        ) filter_article USING (article_key, reporting_from)
    {% endif %}
{% endfor %}
WHERE purchases.movedate BETWEEN '{{ period_start }}' AND '{{ period_end }}'
{% for filter_key, filter_data in filters.items() %}
    {% if filter_data["type"] is not none and filter_data["type"] not in ("flta", "flth") %}
        {% if filter_data["type"] == "fltm" %}
            AND id_rec IN (SELECT id_rec FROM (
            {% if filter_data["ddl"]["cte"] %}
                {{ filter_data["ddl"]["cte"] }}
            {% endif %}
            {% if filter_data["ddl"]["queries"]|length > 0 %}
                {{ filter_data["ddl"]["queries"][0] }} AND movedate BETWEEN '{{ period_start }}' AND '{{ period_end }}'
                {% for query in filter_data["ddl"]["queries"][1:] %}
                    UNION ALL
                    {{ query }} AND movedate BETWEEN '{{ period_start }}' AND '{{ period_end }}'
                {% endfor %}
            {% endif %}
            ))
            {% elif filter_data["type"] == "flts" %}
            AND id_shop IN (SELECT id_shop FROM (
            {% if filter_data["ddl"]["cte"] %}
                {{ filter_data["ddl"]["cte"] }}
            {% endif %}
            {% if filter_data["ddl"]["queries"]|length > 0 %}
                {{ filter_data["ddl"]["queries"][0] }}
                {% for query in filter_data["ddl"]["queries"][1:] %}
                    UNION ALL
                    {{ query }}
                {% endfor %}
            {% endif %}
            ))
        {% endif %}
    {% endif %}
{% endfor %}

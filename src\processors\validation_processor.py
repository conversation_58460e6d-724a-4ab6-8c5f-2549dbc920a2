"""
Validation processor for the application.

This module provides a processor for validating axis, filter, period, and facts data.
It handles validation of data structures before they are passed to the axis processor
for further processing and population.
"""

import logging
from datetime import datetime
from typing import Dict, Optional, List, Any, Tuple, Union

from src.models.axis import AxisData, FilterData, Period, FactsData, SUFactData
from src.models.kpi import JobParameters, ValidationResult
from src.core.exceptions import ValidationError
from src.core.config import config


class ValidationProcessor:
    """
    Class to handle validation of axis, filter, period, and facts data.

    This class provides functionality to validate data structures before they are
    passed to the axis processor for further processing and population.
    """

    def __init__(self):
        """Initialize the ValidationProcessor."""
        self.logger = logging.getLogger(self.__class__.__name__)

    def validate_object(
        self, key: str, value: Any, object_type: str
    ) -> Union[AxisData, FilterData]:
        """
        Generic method to validate and parse an object value (axis or filter).

        Args:
            key: Object key
            value: Object value (string, dictionary, or model)
            object_type: Type of object ('axis' or 'filter')

        Returns:
            Validated model (AxisData or FilterData)

        Raises:
            ValueError: If the object is invalid
        """
        # Determine which model to use based on object_type
        model_class = AxisData if object_type == "axis" else FilterData

        # If value is None or empty string, return empty model
        if not value:
            return model_class()

        # If value is already the correct model, return it
        if isinstance(value, model_class):
            return value

        # If value is a dictionary, validate and convert to model
        if isinstance(value, dict):
            try:
                # Ensure it has the required fields
                if "database" in value and "type" in value and "name" in value:
                    # Make sure it has a full_name field
                    if (
                        "full_name" not in value
                        and value["database"]
                        and value["type"]
                        and value["name"]
                    ):
                        value["full_name"] = (
                            f"{value['database']}.{value['type']}_{value['name']}"
                        )
                    # Create model
                    return model_class(**value)
                else:
                    self.logger.warning(
                        f"Dictionary for {key} is missing required fields"
                    )
                    return model_class()
            except Exception as e:
                self.logger.error(
                    f"Error validating {object_type} dictionary {key}: {e}"
                )
                return model_class()

        # If value is a string, parse it
        try:
            # Split into database and remaining parts
            db_parts = value.split(".")
            if len(db_parts) != 2:
                raise ValueError(
                    f"Invalid {object_type} format: {value}. Expected format: database.type_name"
                )

            database = db_parts[0]
            remaining = db_parts[1]

            # Validate database name
            if " " in database or not database.replace("_", "").isalnum():
                raise ValueError(
                    f"Invalid database name: {database}. Use only letters, numbers, and underscores."
                )

            # Determine valid types based on object_type
            if object_type == "axis":
                valid_prefixes = ["axsm", "axsa", "axss", "axsh"]
            else:  # object_type == 'filter'
                valid_prefixes = ["fltm", "flta", "flts", "flth"]

            # Find the prefix in the remaining part
            prefix_found = False
            obj_type = None
            obj_name = None

            for prefix in valid_prefixes:
                if remaining.startswith(f"{prefix}_"):
                    prefix_found = True
                    obj_type = prefix
                    obj_name = remaining[len(prefix) + 1 :]  # +1 for the underscore
                    break

            # If no valid prefix was found, try to extract it from the first 4 characters
            if not prefix_found:
                if (
                    len(remaining) <= 5
                ):  # Need at least 5 chars: 4 for prefix + 1 for underscore
                    raise ValueError(
                        f"Invalid {object_type} format: {remaining}. Expected format: prefix_name"
                    )

                potential_prefix = remaining[:4]
                if potential_prefix in valid_prefixes and remaining[4] == "_":
                    obj_type = potential_prefix
                    obj_name = remaining[5:]
                else:
                    raise ValueError(
                        f"Invalid {object_type} type: {potential_prefix}. Must be one of: {', '.join(valid_prefixes)}"
                    )

            # Validate name
            if not obj_name:
                raise ValueError(f"Invalid {object_type} name: empty name after prefix")

            if " " in obj_name or not obj_name.replace("_", "").isalnum():
                raise ValueError(
                    f"Invalid {object_type} name: {obj_name}. Use only letters, numbers, and underscores."
                )

            full_name = f"{database}.{obj_type}_{obj_name}"

            # Create model
            return model_class(
                database=database,
                type=obj_type,
                name=obj_name,
                full_name=full_name,
                ddl={"cte": "", "queries": []},
            )

        except (AttributeError, TypeError) as e:
            self.logger.error(f"Error parsing {object_type} {key}: {e}")
            return model_class()

    def validate_axis(self, key: str, value: Any) -> AxisData:
        """
        Validate and parse an axis value.

        Args:
            key: Axis key
            value: Axis value (string, dictionary, or AxisData model)

        Returns:
            Validated AxisData model

        Raises:
            ValueError: If the axis is invalid
        """
        result = self.validate_object(key, value, "axis")
        if not isinstance(result, AxisData):
            raise TypeError(f"Expected AxisData, got {type(result).__name__}")
        return result

    def validate_filter(self, key: str, value: Any) -> FilterData:
        """
        Validate and parse a filter value.

        Args:
            key: Filter key
            value: Filter value (string, dictionary, or FilterData model)

        Returns:
            Validated FilterData model

        Raises:
            ValueError: If the filter is invalid
        """
        result = self.validate_object(key, value, "filter")
        if not isinstance(result, FilterData):
            raise TypeError(f"Expected FilterData, got {type(result).__name__}")
        return result

    def validate_period(self, period_tuple: Tuple[str, str, str]) -> Period:
        """
        Validate and convert a period tuple to a Period model.

        Args:
            period_tuple: Tuple containing (label, date_start, date_end)
                         where label is the period name, date_start and date_end are
                         date strings in 'YYYY-MM-DD' format

        Returns:
            Validated Period model

        Raises:
            ValueError: If the period is invalid
        """
        if len(period_tuple) != 3:
            error_msg = f"Invalid period tuple format: {period_tuple}. Expected (label, date_start, date_end)"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        label, date_start, date_end = period_tuple

        # Validate date format and order
        try:
            # Validate that dates are in the correct format (YYYY-MM-DD)
            start_date = datetime.strptime(date_start, "%Y-%m-%d")
            end_date = datetime.strptime(date_end, "%Y-%m-%d")

            # Validate that start date is before or equal to end date
            if start_date > end_date:
                error_msg = f"Period start date ({date_start}) is after end date ({date_end}) for period '{label}'"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

        except ValueError as e:
            if "does not match format" in str(e):
                error_msg = f"Invalid date format in period '{label}': {str(e)}. Expected format: YYYY-MM-DD"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            else:
                # Re-raise other ValueError exceptions
                raise

        return Period(label=label, date_start=date_start, date_end=date_end)

    def validate_axes_count(self, axes: Dict[str, AxisData]) -> None:
        """
        Validate that there is at most one axis of type 'axsh' and one of type 'axss'.

        Args:
            axes: Dictionary of axis key to AxisData model

        Raises:
            ValidationError: If there is more than one axis of type 'axsh' or 'axss'
        """
        axsh_count = 0
        axsh_keys = []
        axss_count = 0
        axss_keys = []

        for axis_key, axis_data in axes.items():
            if axis_data.type == "axsh":
                axsh_count += 1
                axsh_keys.append(axis_key)
            elif axis_data.type == "axss":
                axss_count += 1
                axss_keys.append(axis_key)

        if axsh_count > 1:
            error_msg = f"Only one axis of type 'axsh' is allowed. Found {axsh_count} axes: {', '.join(axsh_keys)}"
            self.logger.error(error_msg)
            raise ValidationError(error_msg)
        if axss_count > 1:
            error_msg = f"Only one axis of type 'axss' is allowed. Found {axss_count} axes: {', '.join(axss_keys)}"
            self.logger.error(error_msg)
            raise ValidationError(error_msg)

    def validate_su_fact(
        self, su_fact_name: Optional[str] = None, dialect: str = "clickhouse"
    ) -> Optional[SUFactData]:
        """
        Validate and retrieve SU fact data for a specific SU fact name.

        Args:
            su_fact_name: Name of the SU fact to validate and retrieve
            dialect: Database dialect to use for the formula ("clickhouse" or "postgres")

        Returns:
            SUFactData model containing SU fact information or None if not found
        """
        if not su_fact_name:
            return None

        # Get SU fact data from configuration
        if su_fact_name not in config.su_fact:
            self.logger.warning(f"SU fact '{su_fact_name}' not found in configuration")
            return None

        # Get the SU fact data from configuration
        su_fact_config = config.su_fact.get(su_fact_name, {})

        # Get the formula based on the dialect
        formula = su_fact_config.get(dialect.lower())
        if not formula:
            self.logger.warning(
                f"Formula for dialect '{dialect}' not found for SU fact '{su_fact_name}'"
            )
            return None

        # Create SUFactData model
        su_fact_data = SUFactData(
            su_name=su_fact_name,
            measure=su_fact_config.get("measure", ""),
            dialect=dialect.lower(),
            formula=formula,
        )

        self.logger.info(
            f"Using su_fact '{su_fact_name}' with formula: {su_fact_data.formula}"
        )

        return su_fact_data

    def validate_objects(
        self, objects: Dict[str, Any], object_type: str
    ) -> Dict[str, Union[AxisData, FilterData]]:
        """
        Generic method to validate all objects (axes or filters) in a dictionary.

        Note: Empty string values should be filtered out before calling this method.
        This is handled in the validate_axes and validate_filters methods.
        None values are filtered out in this method.

        Args:
            objects: Dictionary of object key to object value
            object_type: Type of object ('axis' or 'filter')

        Returns:
            Dictionary of validated models

        Raises:
            ValidationError: If any object is invalid
        """
        validated_objects = {}
        none_values_count = 0

        for key, value in objects.items():
            # Skip None values
            if value is None:
                none_values_count += 1
                continue

            try:
                validate_method = (
                    self.validate_axis
                    if object_type == "axis"
                    else self.validate_filter
                )
                validated_objects[key] = validate_method(key, value)
            except ValueError as e:
                error_msg = f"Invalid {object_type} {key}: {e}"
                self.logger.error(error_msg)
                # Raise without chaining to avoid nested exceptions
                raise ValidationError(error_msg)

        # Log if any None values were skipped
        if none_values_count > 0:
            self.logger.info(
                f"Skipped {none_values_count} {object_type}s with None values"
            )

        # For axes, validate that there is at most one axis of type 'axsh' and one of type 'axss'
        if object_type == "axis":
            self.validate_axes_count(validated_objects)

        return validated_objects

    def validate_axes(self, axes: Dict[str, Any]) -> Dict[str, AxisData]:
        """
        Validate all axes in a dictionary.

        Args:
            axes: Dictionary of axis key to axis value (string, dictionary, or AxisData model)

        Returns:
            Dictionary of validated AxisData models with empty string values filtered out

        Raises:
            ValidationError: If any axis is invalid
        """
        # Filter out axes with empty string values
        filtered_axes = {k: v for k, v in axes.items() if v != ""}

        # Log if any axes were filtered out
        if len(filtered_axes) < len(axes):
            self.logger.info(
                f"Filtered out {len(axes) - len(filtered_axes)} axes with empty string values"
            )

        return self.validate_objects(filtered_axes, "axis")

    def validate_filters(self, filters: Dict[str, Any]) -> Dict[str, FilterData]:
        """
        Validate all filters in a dictionary.

        Args:
            filters: Dictionary of filter key to filter value (string, dictionary, or FilterData model)

        Returns:
            Dictionary of validated FilterData models with empty string values filtered out

        Raises:
            ValidationError: If any filter is invalid
        """
        # Filter out filters with empty string values
        filtered_filters = {k: v for k, v in filters.items() if v != ""}

        # Log if any filters were filtered out
        if len(filtered_filters) < len(filters):
            self.logger.info(
                f"Filtered out {len(filters) - len(filtered_filters)} filters with empty string values"
            )

        return self.validate_objects(filtered_filters, "filter")

    def validate_periods(
        self, periods: List[Union[Dict[str, str], Period]]
    ) -> List[Period]:
        """
        Validate all periods in a list.

        Args:
            periods: List of period dictionaries.
                    Each dictionary must have 'label', 'date_start', and 'date_end' keys.
                    Example:
                    [
                      {
                        'label': 'Q1 2025',
                        'date_start': '2025-01-01',
                        'date_end': '2025-03-31'
                      },
                      {
                        'label': 'Q2 2025',
                        'date_start': '2025-04-01',
                        'date_end': '2025-06-30'
                      }
                    ]

        Returns:
            List of validated Period models

        Raises:
            ValidationError: If any period is invalid
        """
        validated_periods = []

        for period in periods:
            try:
                # Validate that period is a Period model or a dictionary
                if not isinstance(period, Period):
                    if not isinstance(period, dict):
                        error_msg = (
                            f"Invalid period type: {type(period)}. Expected dictionary."
                        )
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)

                    # Validate required fields
                    if (
                        "label" not in period
                        or "date_start" not in period
                        or "date_end" not in period
                    ):
                        error_msg = "Invalid period dictionary: missing required fields. Expected 'label', 'date_start', and 'date_end'."
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)

                    # Create a tuple from the dictionary and validate it
                    period_tuple = (
                        period["label"],
                        period["date_start"],
                        period["date_end"],
                    )
                    validated_periods.append(self.validate_period(period_tuple))
                else:
                    validated_periods.append(period)
            except ValueError as e:
                error_msg = f"Invalid period: {e}"
                self.logger.error(error_msg)
                # Raise without chaining to avoid nested exceptions
                raise ValidationError(error_msg)

        return validated_periods

    def extract_required_facts(self, facts_data: List[FactsData]) -> List[str]:
        """
        Extract all unique required facts from a list of FactsData models.

        This method extracts unique required facts from the required_facts field
        of each FactsData model in the list. It handles both string and list formats
        for the required_facts field.

        Args:
            facts_data: List of FactsData models to extract required facts from

        Returns:
            List of unique required facts sorted alphabetically
        """
        unique_facts = set()

        for fact in facts_data:
            if fact.required_facts is not None:
                required_facts = fact.required_facts

                # Extract unique required facts based on type
                if isinstance(required_facts, str):
                    facts_list = [
                        f.strip() for f in required_facts.split(",") if f.strip()
                    ]
                    unique_facts.update(facts_list)
                elif isinstance(required_facts, list):
                    unique_facts.update(
                        [
                            f.strip() if isinstance(f, str) else str(f)
                            for f in required_facts
                        ]
                    )

        self.logger.info(f"Extracted {len(unique_facts)} unique required facts")
        return sorted(list(unique_facts))

    def validate_facts_data(
        self,
        facts_data: Optional[
            Union[List[Dict[str, Any]], Dict[str, Dict[str, Any]]]
        ] = None,
        su_fact: Optional[object] = None,
        dialect: str = "clickhouse",
    ):
        """
        Validate facts_data and extract required facts.

        This method validates facts_data and converts it to a list of FactsData models.
        It supports both dictionary format (where keys are fact IDs and values are property dictionaries)
        and list format (list of dictionaries with 'id' field).
        It checks for required fields and validates their values. It also extracts unique
        required facts and validates the SU fact if provided.

        Args:
            facts_data: Optional facts_data in list format
            su_fact: Optional SU fact name to validate
            dialect: Database dialect to use for the SU fact formula

        Returns:
            Tuple containing:
            - List of validated FactsData models
            - List of unique required facts extracted from facts_data
            - SUFactData model with SU fact data if provided and valid, None otherwise

        Raises:
            ValidationError: If any facts_data is invalid or missing required fields
        """
        # Handle None or empty input gracefully
        if not facts_data:
            self.logger.info("No facts_data provided, returning empty list")
            raise ValueError("No facts_data provided")

        # Validate facts_data format
        if not isinstance(facts_data, list):
            error_msg = (
                f"facts_data must be a list or dictionary, got {type(facts_data)}"
            )
            self.logger.error(error_msg)
            raise ValidationError(error_msg)

        validated_facts = []
        unique_facts = set()

        # Process each fact in the list
        for i, fact in enumerate(facts_data):
            try:
                # Validate that fact is a dictionary
                if not isinstance(fact, dict):
                    raise ValueError(f"Expected dictionary, got {type(fact)}")

                # Validate required fields
                if "id" not in fact:
                    raise ValueError("Missing required field 'id'")

                # Validate id is an integer
                if not isinstance(fact.get("id"), int):
                    raise ValueError(
                        f"Field 'id' must be an integer, got {type(fact.get('id'))}"
                    )

                # Validate relative_axis if present
                if "relative_axis" in fact and fact["relative_axis"] is not None:
                    if not isinstance(fact["relative_axis"], str):
                        raise ValueError(
                            f"Field 'relative_axis' must be a string, got {type(fact['relative_axis'])}"
                        )

                # Validate relative_position if present
                if (
                    "relative_position" in fact
                    and fact["relative_position"] is not None
                ):
                    if not isinstance(fact["relative_position"], int):
                        raise ValueError(
                            f"Field 'relative_position' must be an integer, got {type(fact['relative_position'])}"
                        )

                # Validate required_facts if present
                if "required_facts" in fact and fact["required_facts"] is not None:
                    required_facts = fact["required_facts"]
                    if not isinstance(required_facts, (str, list)):
                        raise ValueError(
                            f"Field 'required_facts' must be a string or list, got {type(required_facts)}"
                        )

                    # Extract unique required facts
                    if isinstance(required_facts, str):
                        facts_list = [
                            f.strip() for f in required_facts.split(",") if f.strip()
                        ]
                        unique_facts.update(facts_list)
                    elif isinstance(required_facts, list):
                        unique_facts.update(
                            [
                                f.strip() if isinstance(f, str) else str(f)
                                for f in required_facts
                            ]
                        )

                # Validate divisor if present
                if "divisor" in fact and fact["divisor"] is not None:
                    if not isinstance(fact["divisor"], int):
                        raise ValueError(
                            f"Field 'divisor' must be an integer, got {type(fact['divisor'])}"
                        )

                # Create FactsData model with validated data
                try:
                    validated_fact = FactsData(**fact)
                    validated_facts.append(validated_fact)
                except Exception as e:
                    raise ValueError(f"Failed to create FactsData model: {str(e)}")

            except ValueError as e:
                error_msg = f"Invalid facts_data at index {i}: {e}"
                self.logger.error(error_msg)
                # Raise without chaining to avoid nested exceptions
                raise ValidationError(error_msg)

        # Validate SU fact
        su_fact_data = self.validate_su_fact(su_fact, dialect)

        return validated_facts, sorted(list(unique_facts)), su_fact_data

    def validate_all(
        self,
        job_parameters: JobParameters,
        dialect: str = "clickhouse",
    ):
        """
        Validate all data in a single call.

        Args:
            axes: Dictionary of axis key to axis value
            filters: Dictionary of filter key to filter value
            periods: List of period dictionaries.
                    Each dictionary must have 'label', 'date_start', and 'date_end' keys.
                    Example:
                    [
                      {
                        'label': 'Q1 2025',
                        'date_start': '2025-01-01',
                        'date_end': '2025-03-31'
                      },
                      {
                        'label': 'Q2 2025',
                        'date_start': '2025-04-01',
                        'date_end': '2025-06-30'
                      }
                    ]
            facts_data: Optional facts_data in either dictionary format (where keys are fact IDs and
                       values are property dictionaries) or list format (list of dictionaries with 'id' field)
            su_fact: Optional SU fact name to validate
            id_panel: Optional panel ID to include in the validation result
            dialect: Database dialect to use for the SU fact formula ("clickhouse" or "postgres")

        Raises:
            ValidationError: If any data is invalid
        """
        # Validate axes
        job_parameters.update(axes=self.validate_axes(job_parameters.axes))

        # Validate filters
        job_parameters.update(filters=self.validate_filters(job_parameters.filters))

        # Validate periods
        job_parameters.update(periods=self.validate_periods(job_parameters.periods))

        # Validate facts_data, extract required facts, and validate SU fact
        (
            job_parameters.facts_axis,
            job_parameters.required_facts,
            job_parameters.su_fact_data,
        ) = self.validate_facts_data(
            facts_data=job_parameters.facts_axis,
            su_fact=job_parameters.su_fact_data,
            dialect=dialect,
        )

        self.logger.info(
            f"Validated data: {len(job_parameters.axes)} axes, {len(job_parameters.filters)} filters, "
            f"{len(job_parameters.periods)} periods, {len(job_parameters.facts_axis)} facts, "
            f"{len(job_parameters.required_facts)} required facts, "
            f"su_fact: {job_parameters.su_fact_data.su_name if job_parameters.su_fact_data else 'None'}"
        )

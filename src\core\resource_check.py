import time
import logging
from functools import wraps
from typing import Optional, Callable

class ResourceCheck:
    """Class to handle resource monitoring and waiting logic."""

    def __init__(self, connection, threshold_percent: float = 50, check_interval: int = 20, timeout: int = 6000):
        """
        Initialize ResourceCheck.

        Args:
            connection: Database connection object with current_memory_usage method
            threshold_percent: Maximum acceptable memory usage percentage
            check_interval: Time in seconds to wait between checks
            timeout: Maximum time in seconds to wait for memory to be available before proceeding anyway
        """
        self.connection = connection
        self.threshold = threshold_percent
        self.check_interval = check_interval
        self.timeout = timeout
        self.logger = logging.getLogger(self.__class__.__name__)

    def wait_for_memory(self) -> None:
        """Wait until memory usage falls below threshold or timeout is reached."""
        start_time = time.time()
        wait_count = 0
        
        while True:
            memory_usage = self.connection.current_memory_usage(percent=True)
            elapsed_time = time.time() - start_time
            
            if memory_usage <= self.threshold:
                self.logger.info(
                    f"Current ClickHouse Memory Usage: {memory_usage:.2f}% "
                    "Proceeding with temporary tables creation..."
                )
                break
                
            if elapsed_time >= self.timeout:
                self.logger.warning(
                    f"Timeout reached after {elapsed_time:.1f}s. "
                    f"Proceeding despite high memory usage ({memory_usage:.2f}%)"
                )
                break
            
            wait_count += 1
            self.logger.warning(
                f"Current ClickHouse Memory Usage: {memory_usage:.2f}%! "
                f"Waiting for more memory... (attempt {wait_count}, elapsed: {elapsed_time:.1f}s)"
            )
            time.sleep(self.check_interval)

    @classmethod
    def check_memory(cls, 
                    threshold: float = 50, 
                    interval: int = 20,
                    timeout: int = 600,
                    connection_attr: str = 'connection') -> Callable:
        """
        Decorator to check memory usage before executing a method.

        Args:
            threshold: Maximum acceptable memory usage percentage
            interval: Time in seconds to wait between checks
            timeout: Maximum time in seconds to wait for memory to be available
            connection_attr: Name of the connection attribute in the decorated class

        Returns:
            Callable: Decorated function
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(self, *args, **kwargs):
                connection = getattr(self, connection_attr)
                resource_check = cls(connection, threshold, interval, timeout)
                resource_check.wait_for_memory()
                return func(self, *args, **kwargs)
            return wrapper
        return decorator
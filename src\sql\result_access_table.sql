-- Result Access Tracking Table Schema
-- This table tracks access and usage of KPI results for auditing purposes

CREATE DATABASE IF NOT EXISTS kpi_results;

CREATE TABLE IF NOT EXISTS kpi_results.result_access (
    -- Access tracking
    result_id String,                   -- Reference to the result ID
    access_time DateTime DEFAULT now(), -- When the result was accessed
    user_id String,                     -- User who accessed the result
    access_type Enum('view' = 1, 'export' = 2, 'modify' = 3), -- Type of access
    details String DEFAULT '',          -- Additional access details
    
    -- Indexing for efficient queries
    PRIMARY KEY (result_id, access_time)
)
ENGINE = MergeTree()
ORDER BY (result_id, access_time)
COMMENT = 'Tracking table for KPI result access and usage auditing';

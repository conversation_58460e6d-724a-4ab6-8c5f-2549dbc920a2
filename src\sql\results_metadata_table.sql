-- Results Metadata Table Schema
-- This table stores job-level metadata for KPI analysis results
-- One record per job_id (consolidating all periods)

CREATE DATABASE IF NOT EXISTS kpi_results;

CREATE TABLE IF NOT EXISTS kpi_results.results_metadata (
    -- Primary identifiers
    id String,                          -- Combined result ID for the entire job
    job_id String,                      -- Original job ID from the system
    
    -- Analysis information
    analysis_name String,               -- Name of the KPI analysis
    kpi_type String,                    -- Type of KPI (e.g., 'retention', 'conversion')
    id_panel UInt32,                    -- Panel ID used for the analysis
    
    -- Period information (from first period for compatibility)
    period_name String,                 -- Label of the first period
    period_start Date,                  -- Start date of the first period
    period_end Date,                    -- End date of the first period
    
    -- Timestamps
    created_at DateTime DEFAULT now(),  -- When the job was created
    
    -- Job metadata and configuration
    job_info String DEFAULT '{}',       -- JSON containing all job details, periods, axes, filters
    
    -- Job status tracking
    status Enum('in_progress' = 1, 'done' = 2, 'error' = 3) DEFAULT 'in_progress',
    
    -- Primary key
    PRIMARY KEY (id)
)
ENGINE = MergeTree()
ORDER BY (id, created_at)
COMMENT = 'Metadata table for KPI analysis jobs - one record per job consolidating all periods';

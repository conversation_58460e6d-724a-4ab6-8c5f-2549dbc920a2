"""
ClickHouse Storage Service for KPI Results.

This module implements a dedicated storage service for transferring KPI results
from temporary tables to permanent ClickHouse storage using native operations.

Key Features:
- Two-stage storage approach: temporary tables -> permanent storage
- ClickHouse-native INSERT INTO ... SELECT operations
- Job-level metadata management with status tracking
- Incremental table population strategy
- Support for both pivot and horizontal facts export formats
"""

import json
import logging
import time
from typing import Any, Dict, List, Optional, Tuple

from src.core.connection import ClickHouseConnection
from src.models.axis import Period
from src.models.kpi import KPIType
from src.services.base_service import BaseService
from src.utils.formatting import NumpyJSONEncoder


class ClickHouseStorageService(BaseService):
    """
    Service for storing KPI results in ClickHouse using a two-stage approach.

    Stage 1: Query processor creates temporary tables with results
    Stage 2: This service transfers data to permanent storage using native operations
    """

    def __init__(self, connection: ClickHouseConnection, msg_logger_func=None):
        """
        Initialize the ClickHouse storage service.

        Args:
            connection: ClickHouse connection instance
            msg_logger_func: Optional function for logging messages to the UI
        """
        super().__init__(msg_logger_func)
        self.connection = connection
        self.logger = logging.getLogger(__name__)

    def initialize_job_metadata(
        self,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: KPIType,
        id_panel: int,
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        username: str = "",
        **kwargs,
    ) -> str:
        """
        Initialize job metadata with 'in_progress' status.

        Creates a single metadata record for the entire job (all periods).

        Args:
            job_id: Unique job identifier
            analysis_name: Name of the analysis
            periods: List of periods to be processed
            kpi_type: Type of KPI analysis
            id_panel: Panel ID
            axes: Axes configuration
            filters: Filters configuration
            username: Username
            **kwargs: Additional job parameters

        Returns:
            Combined result ID for the job
        """
        # Generate combined result ID
        timestamp = int(time.time())
        period_names = "_".join([p.label for p in periods])
        result_id = f"job_{job_id}_{timestamp}"

        self.logger.info(
            f"Initializing job metadata for job {job_id} with result_id {result_id}"
        )

        # Create job info with all periods
        periods_info = [
            {
                "name": period.label,
                "start_date": period.date_start,
                "end_date": period.date_end,
            }
            for period in periods
        ]

        job_info = {
            "result_id": result_id,
            "job_id": job_id,
            "analysis_name": analysis_name,
            "kpi_type": str(kpi_type),
            "periods": periods_info,
            "axes": self._serialize_axes(axes),
            "filters": self._serialize_filters(filters),
            "username": username,
            "created_at": time.time(),
        }

        # Add any additional parameters
        job_info.update(kwargs)

        # Insert initial metadata record with 'in_progress' status
        self._insert_job_metadata(
            result_id=result_id,
            job_id=job_id,
            analysis_name=analysis_name,
            periods=periods,
            kpi_type=kpi_type,
            id_panel=id_panel,
            job_info=job_info,
            status="in_progress",
        )

        return result_id

    def transfer_temp_table_to_storage(
        self,
        temp_table_name: str,
        result_id: str,
        period: Period,
        is_first_period: bool = False,
    ) -> Dict[str, Any]:
        """
        Transfer data from temporary table to permanent storage.

        Uses ClickHouse-native INSERT INTO ... SELECT for efficiency.

        Args:
            temp_table_name: Name of the temporary table (without database prefix)
            result_id: Combined result ID for the job
            period: Period information
            is_first_period: Whether this is the first period (creates the table)

        Returns:
            Dictionary with transfer status information
        """
        try:
            self.logger.info(
                f"Transferring temp table {temp_table_name} to storage for period {period.label}"
            )

            # Full temporary table name
            full_temp_table = f"kpi_results.{temp_table_name}"

            # Check if temporary table exists
            if not self._table_exists(full_temp_table):
                error_msg = f"Temporary table {full_temp_table} does not exist"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Get table structure from temporary table
            table_structure = self._get_table_structure(full_temp_table)
            if not table_structure:
                error_msg = (
                    f"Failed to get structure of temporary table {full_temp_table}"
                )
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Permanent table name
            permanent_table = f"kpi_results.data_{result_id}"

            if is_first_period:
                # Create the permanent table using the first period's structure
                self._create_permanent_table(permanent_table, table_structure)

            # Transfer data using INSERT INTO ... SELECT
            rows_transferred = self._transfer_data(full_temp_table, permanent_table)

            self.logger.info(
                f"Successfully transferred {rows_transferred} rows from {temp_table_name} to permanent storage"
            )

            return {
                "success": True,
                "error": None,
                "rows_transferred": rows_transferred,
                "permanent_table": permanent_table,
            }

        except Exception as e:
            error_msg = f"Failed to transfer temp table {temp_table_name}: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "rows_transferred": 0}

    def finalize_job_storage(
        self,
        result_id: str,
        status: str = "done",
        error_info: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Finalize job storage by updating metadata status.

        Args:
            result_id: Combined result ID for the job
            status: Final status ('done' or 'error')
            error_info: Optional error information if status is 'error'

        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info(
                f"Finalizing job storage for result_id {result_id} with status {status}"
            )

            # Get current job info
            job_info = self._get_job_info(result_id)
            if not job_info:
                self.logger.error(f"No job info found for result_id {result_id}")
                return False

            # Update job info with final status
            job_info["status"] = status
            job_info["completed_at"] = time.time()

            if error_info:
                job_info["error"] = error_info

            # Calculate total rows if successful
            if status == "done":
                permanent_table = f"kpi_results.data_{result_id}"
                total_rows = self._count_table_rows(permanent_table)
                job_info["total_rows"] = total_rows

            # Update metadata
            self._update_job_metadata(result_id, status, job_info)

            self.logger.info(
                f"Successfully finalized job storage for result_id {result_id}"
            )
            return True

        except Exception as e:
            self.logger.error(
                f"Failed to finalize job storage for result_id {result_id}: {str(e)}"
            )
            return False

    def _serialize_axes(self, axes: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize axes configuration for storage."""
        try:
            serialized = {}
            for key, value in axes.items():
                if hasattr(value, "model_dump"):
                    # Pydantic model
                    serialized[key] = value.model_dump()
                elif hasattr(value, "dict"):
                    # Pydantic model (older version)
                    serialized[key] = value.dict()
                else:
                    # Regular dict or other serializable type
                    serialized[key] = value
            return serialized
        except Exception as e:
            self.logger.warning(f"Failed to serialize axes: {e}")
            return {}

    def _serialize_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize filters configuration for storage."""
        try:
            serialized = {}
            for key, value in filters.items():
                if hasattr(value, "model_dump"):
                    # Pydantic model
                    serialized[key] = value.model_dump()
                elif hasattr(value, "dict"):
                    # Pydantic model (older version)
                    serialized[key] = value.dict()
                else:
                    # Regular dict or other serializable type
                    serialized[key] = value
            return serialized
        except Exception as e:
            self.logger.warning(f"Failed to serialize filters: {e}")
            return {}

    def _insert_job_metadata(
        self,
        result_id: str,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: KPIType,
        id_panel: int,
        job_info: Dict[str, Any],
        status: str = "in_progress",
    ):
        """Insert initial job metadata record."""
        try:
            # Ensure results database exists
            self.connection.execute_command("CREATE DATABASE IF NOT EXISTS kpi_results")

            # Create results_metadata table if it doesn't exist (will be moved to SQL file later)
            self._ensure_results_metadata_table()

            # Prepare data for insertion
            job_info_json = json.dumps(job_info, cls=NumpyJSONEncoder)

            # Use first period for period-specific fields (for compatibility)
            first_period = periods[0]

            # Escape strings for SQL
            result_id_escaped = self._escape_sql_string(result_id)
            job_id_escaped = self._escape_sql_string(job_id)
            analysis_name_escaped = self._escape_sql_string(analysis_name)
            period_name_escaped = self._escape_sql_string(first_period.label)
            kpi_type_escaped = self._escape_sql_string(str(kpi_type))
            job_info_escaped = self._escape_sql_string(job_info_json)
            status_escaped = self._escape_sql_string(status)

            # Insert metadata record
            insert_query = f"""
                INSERT INTO kpi_results.results_metadata (
                    id, job_id, analysis_name, period_name, period_start, period_end,
                    kpi_type, id_panel, created_at, job_info, status
                )
                VALUES (
                    '{result_id_escaped}', '{job_id_escaped}', '{analysis_name_escaped}',
                    '{period_name_escaped}', '{first_period.date_start}', '{first_period.date_end}',
                    '{kpi_type_escaped}', {id_panel}, now(), '{job_info_escaped}', '{status_escaped}'
                )
            """

            self.connection.execute_command(insert_query)
            self.logger.info(f"Inserted job metadata for result_id {result_id}")

        except Exception as e:
            self.logger.error(f"Failed to insert job metadata: {str(e)}")
            raise

    def _table_exists(self, table_name: str) -> bool:
        """Check if a table exists."""
        try:
            # Parse database and table name
            if "." in table_name:
                database, table = table_name.split(".", 1)
            else:
                database = "default"
                table = table_name

            query = f"""
                SELECT 1 FROM system.tables
                WHERE database = '{database}' AND name = '{table}'
            """

            result = self.connection.get_query_dataframe(query)
            return not result.empty

        except Exception as e:
            self.logger.error(f"Failed to check if table {table_name} exists: {str(e)}")
            return False

    def _get_table_structure(self, table_name: str) -> Optional[List[Dict[str, str]]]:
        """Get table structure information."""
        try:
            query = f"DESCRIBE TABLE {table_name}"
            result = self.connection.get_query_dataframe(query)

            if result.empty:
                return None

            structure = []
            for _, row in result.iterrows():
                structure.append({"name": row["name"], "type": row["type"]})

            return structure

        except Exception as e:
            self.logger.error(
                f"Failed to get structure of table {table_name}: {str(e)}"
            )
            return None

    def _create_permanent_table(self, table_name: str, structure: List[Dict[str, str]]):
        """Create permanent table with given structure."""
        try:
            # Build column definitions
            column_defs = []
            dimension_cols = []

            for col in structure:
                col_name = (
                    col["name"].replace('"', "").replace("'", "").replace(" ", "_")
                )
                col_type = col["type"]
                column_defs.append(f"`{col_name}` {col_type}")

                # Track dimension columns for ORDER BY
                if col_name.endswith("_position_number"):
                    dimension_cols.append(f"`{col_name}`")

            # Add period_name if not already present
            if not any(col["name"] == "period_name" for col in structure):
                column_defs.append("`period_name` String")
                dimension_cols.append("`period_name`")

            # Use dimensions for ORDER BY or fallback to tuple()
            order_by = ", ".join(dimension_cols) if dimension_cols else "tuple()"

            # Create table query
            create_query = f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    {", ".join(column_defs)}
                )
                ENGINE = MergeTree()
                ORDER BY ({order_by})
            """

            self.logger.info(f"Creating permanent table: {table_name}")
            self.logger.debug(f"Create table query: {create_query}")

            self.connection.execute_command(create_query)
            self.logger.info(f"Successfully created permanent table {table_name}")

        except Exception as e:
            self.logger.error(
                f"Failed to create permanent table {table_name}: {str(e)}"
            )
            raise

    def _transfer_data(self, source_table: str, target_table: str) -> int:
        """Transfer data between tables and return row count."""
        try:
            # Use ClickHouse-native INSERT INTO ... SELECT
            transfer_query = f"""
                INSERT INTO {target_table}
                SELECT * FROM {source_table}
            """

            self.logger.info(f"Transferring data from {source_table} to {target_table}")
            self.connection.execute_command(transfer_query)

            # Count transferred rows
            count_query = f"SELECT count() as cnt FROM {source_table}"
            result = self.connection.get_query_dataframe(count_query)
            row_count = result.iloc[0]["cnt"] if not result.empty else 0

            return row_count

        except Exception as e:
            self.logger.error(
                f"Failed to transfer data from {source_table} to {target_table}: {str(e)}"
            )
            raise

    def _get_job_info(self, result_id: str) -> Optional[Dict[str, Any]]:
        """Get job info from metadata."""
        try:
            query = f"""
                SELECT job_info FROM kpi_results.results_metadata
                WHERE id = '{self._escape_sql_string(result_id)}'
            """

            result = self.connection.get_query_dataframe(query)
            if result.empty:
                return None

            job_info_str = result.iloc[0]["job_info"]
            return json.loads(job_info_str)

        except Exception as e:
            self.logger.error(
                f"Failed to get job info for result_id {result_id}: {str(e)}"
            )
            return None

    def _count_table_rows(self, table_name: str) -> int:
        """Count rows in a table."""
        try:
            query = f"SELECT count() as cnt FROM {table_name}"
            result = self.connection.get_query_dataframe(query)
            return result.iloc[0]["cnt"] if not result.empty else 0

        except Exception as e:
            self.logger.error(f"Failed to count rows in table {table_name}: {str(e)}")
            return 0

    def _update_job_metadata(
        self, result_id: str, status: str, job_info: Dict[str, Any]
    ):
        """Update job metadata with final status."""
        try:
            job_info_json = json.dumps(job_info, cls=NumpyJSONEncoder)
            job_info_escaped = self._escape_sql_string(job_info_json)
            status_escaped = self._escape_sql_string(status)
            result_id_escaped = self._escape_sql_string(result_id)

            update_query = f"""
                ALTER TABLE kpi_results.results_metadata
                UPDATE status = '{status_escaped}',
                       job_info = '{job_info_escaped}'
                WHERE id = '{result_id_escaped}'
            """

            self.connection.execute_command(update_query)
            self.logger.info(
                f"Updated job metadata for result_id {result_id} with status {status}"
            )

        except Exception as e:
            self.logger.error(
                f"Failed to update job metadata for result_id {result_id}: {str(e)}"
            )
            raise

    def _ensure_results_metadata_table(self):
        """Ensure the results_metadata table exists with the new schema."""
        try:
            # Load and execute the SQL schema file
            import os

            sql_file_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "sql",
                "results_metadata_table.sql",
            )

            if os.path.exists(sql_file_path):
                with open(sql_file_path, "r", encoding="utf-8") as f:
                    sql_content = f.read()

                # Execute the SQL content (may contain multiple statements)
                # Split by semicolon and execute each statement
                statements = [
                    stmt.strip() for stmt in sql_content.split(";") if stmt.strip()
                ]
                for statement in statements:
                    if statement and not statement.startswith("--"):
                        self.connection.execute_command(statement)

                self.logger.debug(
                    "Ensured results_metadata table exists using SQL file"
                )
            else:
                # Fallback to inline SQL if file doesn't exist
                self.logger.warning(
                    f"SQL file not found: {sql_file_path}, using inline SQL"
                )
                self._create_results_metadata_table_inline()

        except Exception as e:
            self.logger.error(f"Failed to ensure results_metadata table: {str(e)}")
            # Try fallback
            try:
                self._create_results_metadata_table_inline()
            except Exception as fallback_error:
                self.logger.error(f"Fallback also failed: {str(fallback_error)}")
                raise e

    def _create_results_metadata_table_inline(self):
        """Fallback method to create results_metadata table with inline SQL."""
        create_query = """
            CREATE TABLE IF NOT EXISTS kpi_results.results_metadata (
                id String,
                job_id String,
                analysis_name String,
                period_name String,
                period_start Date,
                period_end Date,
                kpi_type String,
                id_panel UInt32,
                created_at DateTime DEFAULT now(),
                job_info String DEFAULT '{}',
                status Enum('in_progress' = 1, 'done' = 2, 'error' = 3) DEFAULT 'in_progress',
                PRIMARY KEY (id)
            )
            ENGINE = MergeTree()
            ORDER BY (id, created_at)
        """

        self.connection.execute_command("CREATE DATABASE IF NOT EXISTS kpi_results")
        self.connection.execute_command(create_query)
        self.logger.debug("Created results_metadata table using inline SQL")

    def _escape_sql_string(self, value: str) -> str:
        """Escape a string for safe SQL insertion."""
        if value is None:
            return ""

        # Convert to string if not already
        str_value = str(value)

        # Escape single quotes by doubling them
        escaped = str_value.replace("'", "''")

        # Escape backslashes
        escaped = escaped.replace("\\", "\\\\")

        return escaped

    def cleanup_temporary_tables(self, result_id: str) -> bool:
        """
        Clean up temporary tables for a specific result ID.

        Args:
            result_id: Combined result ID for the job

        Returns:
            True if cleanup was successful, False otherwise
        """
        try:
            self.logger.info(f"Cleaning up temporary tables for result_id {result_id}")

            # Find all temporary tables for this result_id
            temp_tables_query = f"""
                SELECT name
                FROM system.tables
                WHERE database = 'kpi_results' AND name LIKE 'data_{result_id}_%'
            """

            temp_tables_result = self.connection.get_query_dataframe(temp_tables_query)

            if temp_tables_result.empty:
                self.logger.info(f"No temporary tables found for result_id {result_id}")
                return True

            # Drop each temporary table
            dropped_count = 0
            for _, row in temp_tables_result.iterrows():
                temp_table = row["name"]
                try:
                    drop_query = f"DROP TABLE IF EXISTS kpi_results.{temp_table}"
                    self.connection.execute_command(drop_query)
                    dropped_count += 1
                    self.logger.debug(f"Dropped temporary table {temp_table}")
                except Exception as e:
                    self.logger.warning(
                        f"Failed to drop temporary table {temp_table}: {str(e)}"
                    )

            self.logger.info(
                f"Successfully dropped {dropped_count} temporary tables for result_id {result_id}"
            )
            return True

        except Exception as e:
            self.logger.error(
                f"Failed to cleanup temporary tables for result_id {result_id}: {str(e)}"
            )
            return False

    def get_job_status(self, result_id: str) -> Optional[str]:
        """
        Get the current status of a job.

        Args:
            result_id: Combined result ID for the job

        Returns:
            Job status ('in_progress', 'done', 'error') or None if not found
        """
        try:
            query = f"""
                SELECT status FROM kpi_results.results_metadata
                WHERE id = '{self._escape_sql_string(result_id)}'
            """

            result = self.connection.get_query_dataframe(query)
            if result.empty:
                return None

            return result.iloc[0]["status"]

        except Exception as e:
            self.logger.error(
                f"Failed to get job status for result_id {result_id}: {str(e)}"
            )
            return None

# KPI Results Database Schema

This directory contains SQL DDL files for the KPI results storage system.

## Schema Overview

The KPI results storage system uses a two-stage approach:

1. **Stage 1**: Query processor creates temporary tables with KPI results
2. **Stage 2**: Storage service transfers data to permanent tables using ClickHouse-native operations

## Tables

### results_metadata
- **Purpose**: Stores job-level metadata for KPI analysis results
- **Key Feature**: One record per job_id (consolidating all periods)
- **Status Tracking**: Tracks job status ('in_progress', 'done', 'error')
- **File**: `results_metadata_table.sql`

### result_access
- **Purpose**: Tracks access and usage of KPI results for auditing
- **Key Feature**: Logs all access events with timestamps and user information
- **File**: `result_access_table.sql`

### data_{result_id} (Dynamic Tables)
- **Purpose**: Stores actual KPI result data
- **Key Feature**: One table per job containing all periods
- **Creation**: Created dynamically using first period's schema as template
- **Population**: Incremental insertion of subsequent periods

## Usage

These SQL files are used by the `ClickHouseStorageService` to ensure proper schema management and avoid embedding DDL statements in code.

## Migration Notes

The new schema replaces the old `kpi_results.results` table with:
- Consolidated job-level metadata (one record per job instead of per period)
- Status tracking for better job management
- Cleaner separation between metadata and actual result data

## Export Support

The schema supports both export formats:
- **Pivot Format**: Traditional cross-tab format
- **Horizontal Facts Format**: Normalized fact table format

Both formats are supported through the same underlying table structure with different query patterns during export.

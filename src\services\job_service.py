"""
Job service for the application.

This module provides a service for handling job processing.
"""

import datetime
import hashlib
import time
from typing import Dict, Optional, List, Callable, Any

from src.core.connection import ClickHouseConnection
from src.core.connection_manager import connection_manager
from src.core.resource_check import ResourceCheck
from src.core.exceptions import QueryError
from src.models.kpi import (
    KPIType,
    KPIRequest,
    KPIResult,
    ValidationResult,
    JobParameters,
)
from src.models.axis import Period
from src.services.axis_service import AxisService
from src.services.clickhouse_storage_service import ClickHouseStorageService
from src.services.query_service import QueryService
from src.services.base_service import BaseService
from src.utils.job_api_client import get_job_request
from src.utils.formatting import format_duration
from src.utils.clickhouse_error_parser import (
    parse_clickhouse_error,
    get_simplified_error_message,
)
from src.core.config import config


class JobService(BaseService):
    """
    Service for handling job processing.

    This service encapsulates the functionality of the KPIProcessor class
    and provides a more service-oriented interface.
    """

    def __init__(
        self,
        connection: Optional[ClickHouseConnection] = None,
        axis_service: Optional[AxisService] = None,
        query_service: Optional[QueryService] = None,
        storage_service: Optional[ClickHouseStorageService] = None,
        resource_check: Optional[ResourceCheck] = None,
        msg_logger_func: Optional[Callable] = None,
    ):
        """
        Initialize the job service.

        Args:
            connection: ClickHouse connection
            axis_service: Axis service
            query_service: Query service
            storage_service: Storage service for two-stage approach
            resource_check: Resource check
            msg_logger_func: Optional function for logging messages to the UI
        """
        super().__init__(msg_logger_func)

        # Get the connection from the connection manager if not provided
        self.connection = connection or connection_manager.get_clickhouse_connection()

        # Create the axis service if not provided
        self.axis_service = axis_service or AxisService(
            mg_api_url=config.mg_api_url,
            mg_api_user=config.mg_api_user,
            mg_api_password=config.mg_api_password,
            msg_logger_func=msg_logger_func,
        )

        # Create the query service if not provided
        self.query_service = query_service or QueryService(
            connection=self.connection,
            msg_logger_func=msg_logger_func,
        )

        # Create the storage service for two-stage approach
        self.storage_service = storage_service or ClickHouseStorageService(
            connection=self.connection,
            msg_logger_func=msg_logger_func,
        )

        # Create the resource check if not provided
        self.resource_check = resource_check or ResourceCheck(self.connection)

    def get_job_request(self, job_id: int) -> Dict[str, Any]:
        """
        Get the job request for the specified job ID.

        Args:
            job_id: Job ID

        Returns:
            Job request dictionary
        """
        self.log_message(f"Getting job request for job {job_id}")

        return get_job_request(job_id)

    def create_kpi_request(self, job_request_dict: Dict[str, Any]) -> KPIRequest:
        """
        Create a KPIRequest model from a job request dictionary.

        Args:
            job_request_dict: Job request dictionary

        Returns:
            KPIRequest model
        """
        self.log_message("Creating KPI request model")

        # If facts_axis is not in the request but we have default facts_axis, add it to the request
        if job_request_dict["facts_axis"] is None:
            self.logger.info("No facts_axis in request, using default facts_axis")
            # Use the default facts_axis dictionary directly
            job_request_dict["facts_axis"] = config.DEFAULT_FACTS_AXIS

        # Default catman kpis until catman measures selection is implemented
        if job_request_dict["kpi_type"] == "catman_kpi":
            job_request_dict["facts_axis"] = config.DEFAULT_FACTS_AXIS_CATMAN

        # Create KPIRequest model directly from the job request dictionary
        return KPIRequest(**job_request_dict)

    def generate_combined_result_id(
        self,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: KPIType,
    ) -> str:
        """
        Generate a combined result ID for all periods.

        Args:
            job_id: Job ID
            analysis_name: Analysis name
            periods: List of Period model objects
            kpi_type: KPI type

        Returns:
            Combined result ID
        """
        # Create a unique ID based on the analysis name, job ID, and timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        period_names = "_".join([p.label for p in periods])
        hash_input = f"{analysis_name}_{period_names}_{kpi_type}_{timestamp}"
        hash_part = hashlib.md5(hash_input.encode()).hexdigest()[:8]  # Use shorter hash
        return f"job_{job_id}_{hash_part}"

    def process_job(self, job_id: int, username: str) -> KPIResult:
        """
        Process a KPI job.

        Args:
            job_id: Job ID
            username: Username (may be overridden by job metadata)

        Returns:
            KPIResult model
        """
        # Start timing for total execution
        start_time = time.time()

        # Get and parse job request
        self.log_message("Fetching job request data")
        job_request_dict = self.get_job_request(job_id)
        self.logger.info(f"Job request: {job_request_dict}")

        # Validate job request
        if not job_request_dict:
            error_message = "KPI: job request is empty!"
            self.logger.error(error_message)
            return KPIResult(result_ids=[], errors=[error_message])

        # Get actual username from job metadata
        job_metadata = job_request_dict.get("_metadata", {})
        actual_username = job_metadata.get("user_name", username)
        if actual_username and actual_username != username:
            self.logger.info(
                f"Using actual username from job metadata: {actual_username} (was: {username})"
            )
            username = actual_username

        # Create KPIRequest model
        kpi_request = self.create_kpi_request(job_request_dict)

        # Create JobParameters model
        job_parameters = JobParameters(
            job_id=str(job_id),
            **kpi_request.model_dump(),
        )

        # Initialize result summary
        result_summary = KPIResult(result_ids=[], errors=[])

        try:
            # Validate and prepare all data for the query builder in a single call
            self.log_message("Validating and preparing data for query builder")
            self.axis_service.validate_and_prepare_data(
                job_parameters=job_parameters,
            )

            # Log the validation results
            self.log_message(
                f"Validated and prepared data for query builder: {len(job_parameters.axes)} axes, {len(job_parameters.filters)} filters, {len(job_parameters.facts_axis or [])} facts, {len(job_parameters.required_facts)} required facts"
            )

            # Create query builder with validated data directly from JobParameters model
            self.log_message("Creating query builder with validated data")
            query_builder = self.query_service.create_query_builder(
                job_parameters=job_parameters,
            )

            # Create query processor
            self.log_message("Creating query processor")
            query_processor = self.query_service.create_query_processor(query_builder)

            # Generate a combined result ID for all periods
            self.log_message("Generating result ID for data storage")
            combined_result_id = self.generate_combined_result_id(
                str(job_id),
                kpi_request.analysis_name,
                job_parameters.periods,
                kpi_request.kpi_type,
            )
            # Add to result_ids - the final data table creation will be skipped if there are errors
            result_summary.result_ids.append(combined_result_id)
            job_parameters.update(combined_result_id=combined_result_id)

            # Process all periods
            for period in job_parameters.periods:
                # Log progress
                self.log_message(f"Processing period: {period.label}")
                job_parameters.update(period=period)

                # Process the period
                try:
                    # Check system resources before processing
                    self.log_message(
                        "Checking system resources before processing query..."
                    )
                    self.resource_check.wait_for_memory()

                    _, error_info = self.query_service.process_query(
                        query_processor=query_processor, job_parameters=job_parameters
                    )

                    # If error_info is returned, add it to the result_summary errors
                    if error_info:
                        self.logger.error(
                            f"Error detected during query processing: {error_info}"
                        )
                        result_summary.add_error(
                            error=error_info.get("message", "Unknown error"),
                            error_code=error_info.get("error_code"),
                            period=error_info.get("period"),
                        )
                except QueryError as e:
                    # Handle ClickHouse errors specifically
                    error_msg = (
                        f"ClickHouse query failed for period {period.label}: {e}"
                    )
                    self.logger.error(error_msg, exc_info=True)

                    # Add simplified error information to result
                    error_code = getattr(e, "error_code", None)
                    simplified_message = get_simplified_error_message(
                        str(e), error_code
                    )
                    result_summary.add_error(
                        error=simplified_message,
                        error_code=error_code,
                        period=period.label,
                    )

                    # Stop processing on ClickHouse errors
                    self.logger.error(
                        f"Stopping KPI processing due to ClickHouse error (code: {getattr(e, 'error_code', 'unknown')})"
                    )
                    break

                except Exception as e:
                    # Handle other errors
                    error_msg = f"Failed to process period {period.label}: {e}"
                    self.logger.error(error_msg, exc_info=True)

                    # Check if it's a ClickHouse error that wasn't caught as QueryError
                    error_info = parse_clickhouse_error(str(e))
                    if error_info["error_code"] is not None:
                        # It's a ClickHouse error - use simplified message
                        simplified_message = get_simplified_error_message(
                            str(e), error_info["error_code"]
                        )
                        result_summary.add_error(
                            error=simplified_message,
                            error_code=error_info["error_code"],
                            period=period.label,
                        )
                        # Stop processing
                        self.logger.error(
                            f"Stopping KPI processing due to ClickHouse error (code: {error_info['error_code']})"
                        )
                        break
                    else:
                        # Regular error, add to errors and continue
                        simplified_message = get_simplified_error_message(str(e))
                        result_summary.add_error(
                            error=simplified_message, period=period.label
                        )
                        # Continue with next period

            # Log completion
            self.log_message("Done")

        except QueryError as e:
            # Handle ClickHouse errors specifically
            error_msg = (
                f"Critical failure in KPI processing due to ClickHouse error: {e}"
            )
            self.logger.error(error_msg, exc_info=True)

            # Add simplified error information to result
            error_code = getattr(e, "error_code", None)
            simplified_message = get_simplified_error_message(str(e), error_code)
            result_summary.add_error(
                error=simplified_message,
                error_code=error_code,
            )

        except Exception as e:
            # Check if it's a ClickHouse error that wasn't caught as QueryError
            error_info = parse_clickhouse_error(str(e))
            if error_info["error_code"] is not None:
                # It's a ClickHouse error
                error_msg = f"Critical failure in KPI processing due to ClickHouse error (code: {error_info['error_code']}): {e}"
                self.logger.error(error_msg, exc_info=True)

                # Add simplified error information to result
                simplified_message = get_simplified_error_message(
                    str(e), error_info["error_code"]
                )
                result_summary.add_error(
                    error=simplified_message,
                    error_code=error_info["error_code"],
                )
            else:
                # Regular error
                error_msg = f"Critical failure in KPI processing: {e}"
                self.logger.error(error_msg, exc_info=True)
                simplified_message = get_simplified_error_message(str(e))
                result_summary.add_error(error=simplified_message)

        # Log the result status
        if not result_summary.errors:
            self.logger.info(
                f"Successfully processed result: {job_parameters.combined_result_id}"
            )
        else:
            error_count = len(result_summary.errors)
            # Check if we have result_ids even though there were errors
            if result_summary.result_ids:
                self.logger.error(
                    f"Job completed with {error_count} error(s) - results stored in ClickHouse with ID(s): {', '.join(result_summary.result_ids)}"
                )
                self.logger.warning(
                    "Final result table was not created due to errors, but error records were stored in the results table"
                )
            else:
                self.logger.error(
                    f"Job failed with {error_count} error(s) and no results were stored in ClickHouse"
                )
                self.logger.error(
                    "Critical failure: No records were created in the results table"
                )

        # Calculate and log total execution time
        duration = format_duration((time.time() - start_time) * 1000)
        self.log_message(f"Total execution time: {duration}")

        return result_summary

    def process_job_two_stage(self, job_id: int, username: str) -> KPIResult:
        """
        Process a KPI job using the two-stage storage approach.

        Stage 1: Create temporary tables with query results for each period
        Stage 2: Transfer data to permanent storage and consolidate

        Args:
            job_id: Job ID
            username: Username (may be overridden by job metadata)

        Returns:
            KPIResult model
        """
        # Start timing for total execution
        start_time = time.time()

        # Get and parse job request
        self.log_message("Fetching job request data")
        job_request_dict = self.get_job_request(job_id)
        self.logger.info(f"Job request: {job_request_dict}")

        # Validate job request
        if not job_request_dict:
            error_message = "KPI: job request is empty!"
            self.logger.error(error_message)
            return KPIResult(result_ids=[], errors=[error_message])

        # Get actual username from job metadata
        job_metadata = job_request_dict.get("_metadata", {})
        actual_username = job_metadata.get("user_name", username)
        if actual_username and actual_username != username:
            self.logger.info(
                f"Using actual username from job metadata: {actual_username} (was: {username})"
            )
            username = actual_username

        # Create KPIRequest model
        kpi_request = self.create_kpi_request(job_request_dict)

        # Create JobParameters model
        job_parameters = JobParameters(
            job_id=str(job_id),
            **kpi_request.model_dump(),
        )

        # Initialize result summary
        result_summary = KPIResult(result_ids=[], errors=[])

        try:
            # Validate and prepare all data for the query builder
            self.log_message("Validating and preparing data for query builder")
            self.axis_service.validate_and_prepare_data(job_parameters=job_parameters)

            # Log the validation results
            self.log_message(
                f"Validated and prepared data for query builder: {len(job_parameters.axes)} axes, "
                f"{len(job_parameters.filters)} filters, {len(job_parameters.facts_axis or [])} facts, "
                f"{len(job_parameters.required_facts)} required facts"
            )

            # Create query builder with validated data
            self.log_message("Creating query builder with validated data")
            query_builder = self.query_service.create_query_builder(
                job_parameters=job_parameters
            )

            # Create query processor with storage service
            self.log_message("Creating query processor")
            query_processor = self.query_service.create_query_processor(query_builder)
            # Add storage service to the query processor
            query_processor.storage_service = self.storage_service

            # Initialize job metadata in storage service
            self.log_message("Initializing job metadata")
            combined_result_id = self.storage_service.initialize_job_metadata(
                job_id=str(job_id),
                analysis_name=kpi_request.analysis_name,
                periods=job_parameters.periods,
                kpi_type=kpi_request.kpi_type,
                id_panel=job_parameters.id_panel,
                axes=job_parameters.axes,
                filters=job_parameters.filters,
                username=username,
            )

            # Add to result_ids
            result_summary.result_ids.append(combined_result_id)
            job_parameters.update(combined_result_id=combined_result_id)

            self.logger.info(
                f"Initialized job metadata with result_id: {combined_result_id}"
            )

            # Process all periods using two-stage approach
            temp_tables_created = []
            for period_idx, period in enumerate(job_parameters.periods):
                is_first_period = period_idx == 0

                # Log progress
                self.log_message(
                    f"Processing period {period_idx + 1}/{len(job_parameters.periods)}: {period.label}"
                )
                job_parameters.update(period=period)

                try:
                    # Check system resources before processing
                    self.log_message(
                        "Checking system resources before processing query..."
                    )
                    self.resource_check.wait_for_memory()

                    # Generate queries for this period
                    from src.processors.query_builder_processor import (
                        QueryBuilderProcessor,
                    )

                    query_builder_processor = QueryBuilderProcessor(
                        query_processor.query_builder
                    )
                    queries, query_error = query_builder_processor.generate_queries(
                        job_parameters.period, job_parameters.kpi_type
                    )

                    if query_error:
                        self.logger.error(
                            f"Failed to generate queries for period {period.label}: {query_error}"
                        )
                        result_summary.add_error(
                            error=query_error.get(
                                "message", "Failed to generate queries"
                            ),
                            error_code=query_error.get("error_code"),
                            period=period.label,
                        )
                        break

                    # Process period using two-stage approach
                    temp_table_name, error_info = (
                        query_processor.process_query_two_stage(
                            job_parameters=job_parameters,
                            queries=queries,
                            result_id=combined_result_id,
                            is_first_period=is_first_period,
                        )
                    )

                    if error_info:
                        self.logger.error(
                            f"Error processing period {period.label}: {error_info}"
                        )
                        result_summary.add_error(
                            error=error_info.get("message", "Unknown error"),
                            error_code=error_info.get("error_code"),
                            period=error_info.get("period"),
                        )
                        break  # Stop processing on error
                    else:
                        temp_tables_created.append(temp_table_name)
                        self.logger.info(
                            f"Successfully processed period {period.label}"
                        )

                except Exception as e:
                    error_msg = f"Failed to process period {period.label}: {e}"
                    self.logger.error(error_msg, exc_info=True)

                    # Check if it's a ClickHouse error
                    error_info = parse_clickhouse_error(str(e))
                    if error_info["error_code"] is not None:
                        simplified_message = get_simplified_error_message(
                            str(e), error_info["error_code"]
                        )
                        result_summary.add_error(
                            error=simplified_message,
                            error_code=error_info["error_code"],
                            period=period.label,
                        )
                        break  # Stop processing on ClickHouse errors
                    else:
                        simplified_message = get_simplified_error_message(str(e))
                        result_summary.add_error(
                            error=simplified_message, period=period.label
                        )
                        break  # Stop processing on any error in two-stage approach

            # Finalize job storage
            if result_summary.errors:
                # Finalize with error status
                self.log_message("Finalizing job storage with error status")
                error_info = {
                    "errors": [error.model_dump() for error in result_summary.errors],
                    "error_count": len(result_summary.errors),
                }
                self.storage_service.finalize_job_storage(
                    result_id=combined_result_id, status="error", error_info=error_info
                )
            else:
                # Finalize with success status
                self.log_message("Finalizing job storage with success status")
                self.storage_service.finalize_job_storage(
                    result_id=combined_result_id, status="done"
                )

            # Clean up temporary tables
            self.log_message("Cleaning up temporary tables")
            self.storage_service.cleanup_temporary_tables(combined_result_id)

            # Log completion
            self.log_message("Two-stage job processing completed")

        except Exception as e:
            error_msg = f"Critical failure in two-stage KPI processing: {e}"
            self.logger.error(error_msg, exc_info=True)

            # Finalize with error status if we have a result_id
            if (
                hasattr(job_parameters, "combined_result_id")
                and job_parameters.combined_result_id
            ):
                try:
                    error_info = {"critical_error": str(e)}
                    self.storage_service.finalize_job_storage(
                        result_id=job_parameters.combined_result_id,
                        status="error",
                        error_info=error_info,
                    )
                except Exception as finalize_error:
                    self.logger.error(
                        f"Failed to finalize job storage after critical error: {finalize_error}"
                    )

            simplified_message = get_simplified_error_message(str(e))
            result_summary.add_error(error=simplified_message)

        # Log the result status
        if not result_summary.errors:
            self.logger.info(
                f"Successfully processed two-stage job: {job_parameters.combined_result_id}"
            )
        else:
            error_count = len(result_summary.errors)
            if result_summary.result_ids:
                self.logger.error(
                    f"Two-stage job completed with {error_count} error(s) - "
                    f"metadata stored with ID(s): {', '.join(result_summary.result_ids)}"
                )
            else:
                self.logger.error(f"Two-stage job failed with {error_count} error(s)")

        # Calculate and log total execution time
        duration = format_duration((time.time() - start_time) * 1000)
        self.log_message(f"Total two-stage execution time: {duration}")

        return result_summary
